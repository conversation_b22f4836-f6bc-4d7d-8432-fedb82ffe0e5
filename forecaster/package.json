{"name": "forecaster", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:visual": "percy exec -- playwright test", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^8.46.0", "@use-gesture/react": "^10.3.1", "@vercel/analytics": "^1.4.1", "@xmldom/xmldom": "^0.9.8", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.1.0", "immer": "^10.1.1", "ioredis": "^5.4.1", "isomorphic-dompurify": "^2.19.0", "jspdf": "^3.0.1", "lucide-react": "^0.513.0", "mongodb": "^6.16.0", "next": "15.3.3", "next-themes": "^0.4.6", "ol": "^10.5.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "sonner": "^2.0.4", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "winston": "^3.17.0", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@percy/cli": "^1.30.1", "@percy/playwright": "^1.0.6", "@playwright/test": "^1.49.1", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/chart.js": "^2.9.41", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/ol": "^6.5.3", "@types/react": "^19", "@types/react-dom": "^19", "audit-ci": "^7.1.0", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tailwindcss": "^4", "ts-jest": "^29.2.5", "tw-animate-css": "^1.3.2", "typescript": "^5", "wait-on": "^8.0.3"}}